package net.montoyo.mcef.setup;

import java.io.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class FileListing {

    private ArrayList<String> fileNames = new ArrayList<String>();
    private File location;

    public FileListing(File dir) {
        location = new File(dir, "mcefFiles.lst");

        if(location.exists())
            load();
    }

    public boolean load() {
        try {
            unsafeLoad();
            return true;
        } catch(Throwable t) {
            System.err.println("Coud not read file listing:");
            t.printStackTrace();
            return false;
        }
    }

    private void unsafeLoad() throws Throwable {
        BufferedReader br = new BufferedReader(new FileReader(location));
        String line;

        fileNames.clear();

        while((line = br.readLine()) != null) {
            line = line.trim();

            if(line.length() > 0 && line.charAt(0) != '#' && line.charAt(0) != '.' && line.charAt(0) != '/' && line.charAt(0) != '\\')
                fileNames.add(line);
        }

        SetupUtil.silentClose(br);
    }

    public boolean save() {
        try {
            unsafeSave();
            return true;
        } catch(Throwable t) {
            System.err.println("Coud not write file listing:");
            t.printStackTrace();
            return false;
        }
    }

    private void unsafeSave() throws Throwable {
        if(location.exists())
            SetupUtil.tryDelete(location);

        BufferedWriter bw = new BufferedWriter(new FileWriter(location));
        bw.write("# DO NOT EDIT THIS FILE. IT HAS BEEN AUTOMATICALLY GENERATED.\n");
        bw.write("# This file contains the list of files installed by MCEF.\n");
        bw.write("# If you remove MCEF, they are no longer needed and you can safely remove them,\n");
        bw.write("# or you can let the uninstaller do it for you. Just run the MCEF mod jar using Java.\n\n");

        for(String f : fileNames)
            bw.write(f + "\n");

        SetupUtil.silentClose(bw);
    }

    public void addFile(String f) {
        if(!fileNames.contains(f))
            fileNames.add(f);
    }

    public boolean addZip(String fname) {
        try {
            addZipUnsafe(fname);
            return true;
        } catch(Throwable t) {
            System.err.println("Coud not list file in ZIP archive \"" + fname + "\":");
            t.printStackTrace();
            return false;
        }
    }

    private void addZipUnsafe(String fname) throws Throwable {
        ArrayList<String> files = new ArrayList<String>();
        ZipInputStream zis = new ZipInputStream(new FileInputStream(fname));
        ZipEntry ze;

        while((ze = zis.getNextEntry()) != null) {
            String name = ze.getName();

            if(ze.isDirectory() && (name.endsWith("/") || name.endsWith("\\")))
                files.add(name.substring(0, name.length() - 1));
            else
                files.add(name);
        }

        SetupUtil.silentClose(zis);

        files.sort(new SlashComparator(new DefaultComparator()));
        for(String t: files)
            addFile(t); //Use addFile instead of fileNames.addAll() to remove duplicates
    }

    public Iterator<String> iterator() {
        return fileNames.iterator();
    }

    public boolean selfDestruct() {
        return SetupUtil.tryDelete(location);
    }

}
