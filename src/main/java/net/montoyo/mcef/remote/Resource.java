package net.montoyo.mcef.remote;

import java.io.File;

import net.montoyo.mcef.MCEF;
import net.montoyo.mcef.client.ClientProxy;
import net.montoyo.mcef.utilities.IProgressListener;
import net.montoyo.mcef.utilities.Log;
import net.montoyo.mcef.utilities.Util;

/**
 * A remote resource. Can be downloaded, extracted and checked.
 * <AUTHOR>
 *
 */
public class Resource {

    private String platform;
    private String name;
    private String sum;
    private boolean shouldExtract = false;
    
    /**
     * Constructs a remote resource from its filename and its SHA-1 checksum.
     * 
     * @param name The filename of the resource.
     * @param platform The platform the resource was made for.
     * @param sum The SHA-1 hash of the file.
     */
    public Resource(String name, String sum, String platform) {
        this.name = name;
        this.sum = sum.trim();
        this.platform = platform;
    }
    
    /**
     * Checks if the file exists with comprehensive validation including file readability,
     * size validation, and enhanced checksum verification with detailed logging.
     * If the file couldn't be hashed, false will be returned.
     *
     * @return true if (and only if) the file exists and passes all validation checks.
     */
    public boolean exists() {
        // Force redownload if configured
        if(MCEF.FORCE_REDOWNLOAD) {
            if(MCEF.VERBOSE_RESOURCE_CHECKING) {
                Log.info("Resource %s: Force redownload enabled, skipping validation", name);
            }
            return false;
        }

        File f = new File(ClientProxy.JCEF_ROOT, name);

        // Check basic file existence
        if(!f.exists()) {
            if(MCEF.VERBOSE_RESOURCE_CHECKING) {
                Log.info("Resource %s: File does not exist at %s", name, f.getAbsolutePath());
            }
            return false;
        }

        // Check if file is readable
        if(!f.canRead()) {
            if(MCEF.VERBOSE_RESOURCE_CHECKING) {
                Log.warning("Resource %s: File exists but is not readable at %s", name, f.getAbsolutePath());
            } else {
                Log.warning("Resource %s: File is not readable, will redownload", name);
            }
            return false;
        }

        // Check file size (must be greater than 0)
        long fileSize = f.length();
        if(fileSize <= 0) {
            if(MCEF.VERBOSE_RESOURCE_CHECKING) {
                Log.warning("Resource %s: File has invalid size %d bytes at %s", name, fileSize, f.getAbsolutePath());
            } else {
                Log.warning("Resource %s: File has invalid size, will redownload", name);
            }
            return false;
        }

        // Skip checksum verification if configured
        if(MCEF.SKIP_CHECKSUM_VERIFICATION) {
            if(MCEF.VERBOSE_RESOURCE_CHECKING) {
                Log.info("Resource %s: Skipping checksum verification (size: %d bytes)", name, fileSize);
            }
            return true;
        }

        // Perform checksum verification
        if(MCEF.VERBOSE_RESOURCE_CHECKING) {
            Log.info("Resource %s: Verifying checksum for file (size: %d bytes)", name, fileSize);
        }

        String hash = Util.hash(f);
        if(hash == null) {
            if(MCEF.VERBOSE_RESOURCE_CHECKING) {
                Log.warning("Resource %s: Failed to compute hash for file %s", name, f.getAbsolutePath());
            } else {
                Log.warning("Couldn't hash file %s; assuming it doesn't exist.", f.getAbsolutePath());
            }
            return false;
        }

        boolean checksumValid = hash.equalsIgnoreCase(sum);
        if(MCEF.VERBOSE_RESOURCE_CHECKING) {
            if(checksumValid) {
                Log.info("Resource %s: Checksum verification passed (expected: %s, actual: %s)", name, sum, hash);
            } else {
                Log.warning("Resource %s: Checksum verification failed (expected: %s, actual: %s)", name, sum, hash);
            }
        } else if(!checksumValid) {
            Log.warning("Resource %s: Checksum mismatch, will redownload", name);
        }

        return checksumValid;
    }
    
    /**
     * Downloads the resource from the current mirror.
     * 
     * @param ipl Progress listener. May be null.
     * @return true if the operation was successful.
     */
    public boolean download(IProgressListener ipl) {
        String end = "";
        if(shouldExtract)
            end += ".gz";

        File dst = new File(ClientProxy.JCEF_ROOT, name);
        File parent = dst.getParentFile();

        //ClientProxy.ROOT exists, but this.name might contain some subdirectories that we need to create...
        if(!parent.exists() && !parent.mkdirs())
            Log.warning("Couldn't create directory %s... ignoring this error, but this might cause some issues later...", parent.getAbsolutePath());

        return Util.download(MCEF.VERSION + '/' + platform + '/' + name + end, dst, shouldExtract, ipl);
    }
    
    /**
     * If the resource is a ZIP archive, it may be extracted using this method.
     * 
     * @param ipl Progress listener. May be null.
     * @return true if the operation was successful.
     */
    public boolean extract(IProgressListener ipl) {
        File sourceFile = new File(ClientProxy.JCEF_ROOT, name);
        long fileSize = sourceFile.length();
        String progressMessage;

        if(fileSize > 0) {
            double sizeMB = fileSize / (1024.0 * 1024.0);
            if(sizeMB >= 1.0) {
                progressMessage = String.format("Extracting %s (%.1f MB)", name, sizeMB);
            } else {
                double sizeKB = fileSize / 1024.0;
                progressMessage = String.format("Extracting %s (%.1f KB)", name, sizeKB);
            }
        } else {
            progressMessage = "Extracting " + name;
        }

        Util.secure(ipl).onTaskChanged(progressMessage);
        return Util.extract(sourceFile, new File(ClientProxy.JCEF_ROOT));
    }

    /**
     * Mark the resource as a GZip archive that should be extracted.
     */
    public void setShouldExtract() {
        shouldExtract = true;
        name = name.substring(0, name.length() - 3);
    }
    
    /**
     * Gets the filename of this resource.
     * @return The filename of this resource.
     */
    public String getFileName() {
        return name;
    }

    /**
     * Gets the file size of this resource if it exists locally.
     * @return The file size in bytes, or -1 if the file doesn't exist.
     */
    public long getLocalFileSize() {
        File f = new File(ClientProxy.JCEF_ROOT, name);
        return f.exists() ? f.length() : -1;
    }

    /**
     * Gets the expected checksum for this resource.
     * @return The SHA-1 checksum string.
     */
    public String getExpectedChecksum() {
        return sum;
    }

    /**
     * Returns the File corresponding to the specified resource.
     *
     * @param resName Name of the resource.
     * @return The File containing the location of the specified resource.
     */
    public static File getLocationOf(String resName) {
        return new File(ClientProxy.JCEF_ROOT, resName);
    }

}
